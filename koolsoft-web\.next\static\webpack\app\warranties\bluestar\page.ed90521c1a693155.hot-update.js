"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/bluestar/page",{

/***/ "(app-pages-browser)/./src/app/warranties/bluestar/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/warranties/bluestar/page.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BluestarWarrantyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Eye,FileDown,FileText,MapPin,Phone,Plus,Search,Shield,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * BLUESTAR Warranty Management Page\n * \n * This page displays and manages BLUESTAR-specific warranty workflows.\n * It includes vendor-specific forms, validation rules, and specialized reporting.\n */ function BluestarWarrantyPage() {\n    _s();\n    _s1();\n    const [bluestarWarranties, setBluestarWarranties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [serviceCenters, setServiceCenters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [serviceCenterFilter, setServiceCenterFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('warranties');\n    // Load BLUESTAR warranties and service centers from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BluestarWarrantyPage.useEffect\": ()=>{\n            const loadData = {\n                \"BluestarWarrantyPage.useEffect.loadData\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        // Fetch warranties and service centers in parallel\n                        const [warrantiesResponse, serviceCentersResponse] = await Promise.all([\n                            fetch('/api/warranties?vendor=bluestar', {\n                                credentials: 'include',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                }\n                            }),\n                            fetch('/api/service-centers?vendor=BLUESTAR', {\n                                credentials: 'include',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                }\n                            })\n                        ]);\n                        if (!warrantiesResponse.ok) {\n                            throw new Error('Failed to fetch BLUESTAR warranties');\n                        }\n                        if (!serviceCentersResponse.ok) {\n                            throw new Error('Failed to fetch service centers');\n                        }\n                        const warrantiesData = await warrantiesResponse.json();\n                        const serviceCentersData = await serviceCentersResponse.json();\n                        setBluestarWarranties(warrantiesData.warranties || []);\n                        setServiceCenters(serviceCentersData.serviceCenters || []);\n                        setError(null);\n                    } catch (err) {\n                        console.error('Error loading BLUESTAR data:', err);\n                        setError('Failed to load BLUESTAR warranty data');\n                        setBluestarWarranties([]);\n                        setServiceCenters([]);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"BluestarWarrantyPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"BluestarWarrantyPage.useEffect\"], []);\n    const getStatusBadge = (status, warrantyDate)=>{\n        const today = new Date();\n        const warranty = new Date(warrantyDate);\n        const daysUntilExpiry = Math.ceil((warranty.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        if (status === 'EXPIRED') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                variant: \"destructive\",\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Expired\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 14\n            }, this);\n        }\n        if (daysUntilExpiry <= 30) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                variant: \"secondary\",\n                className: \"flex items-center space-x-1 bg-yellow-100 text-yellow-800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-3 w-3\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Expiring Soon\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                lineNumber: 86,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n            variant: \"secondary\",\n            className: \"flex items-center space-x-1 bg-green-100 text-green-800\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: \"Active\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n            lineNumber: 91,\n            columnNumber: 12\n        }, this);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-IN');\n    };\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-IN', {\n            style: 'currency',\n            currency: 'INR',\n            minimumFractionDigits: 0\n        }).format(amount);\n    };\n    const filteredWarranties = bluestarWarranties.filter((warranty)=>{\n        const matchesSearch = searchTerm === '' || warranty.bslNo.toLowerCase().includes(searchTerm.toLowerCase()) || warranty.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) || warranty.vendorSpecific.bluestarWarrantyCode.toLowerCase().includes(searchTerm.toLowerCase()) || warranty.machines.some((machine)=>machine.serialNumber.toLowerCase().includes(searchTerm.toLowerCase()));\n        const matchesStatus = statusFilter === 'all' || warranty.status === statusFilter;\n        const matchesServiceCenter = serviceCenterFilter === 'all' || warranty.vendorSpecific.bluestarServiceCenter === serviceCenterFilter;\n        return matchesSearch && matchesStatus && matchesServiceCenter;\n    });\n    const handleExport = async ()=>{\n        try {\n            const response = await fetch('/api/warranties/export?vendor=bluestar&format=CSV', {\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to export BLUESTAR warranty data');\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"bluestar-warranties-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n        } catch (error) {\n            console.error('Error exporting BLUESTAR warranty data:', error);\n            setError('Failed to export BLUESTAR warranty data');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    className: \"pb-3 flex flex-row items-center justify-between bg-primary text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"BLUESTAR Warranty Management\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-gray-100\",\n                                    children: \"Manage vendor-specific BLUESTAR warranty workflows and specialized processes\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"secondary\",\n                                    onClick: handleExport,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Export\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"secondary\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                        href: \"/warranties/new?vendor=bluestar\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"New BLUESTAR Warranty\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.Tabs, {\n                        defaultValue: \"warranties\",\n                        value: activeTab,\n                        onValueChange: setActiveTab,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsList, {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                        value: \"warranties\",\n                                        children: \"Warranties\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                        value: \"service-centers\",\n                                        children: \"Service Centers\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsTrigger, {\n                                        value: \"reports\",\n                                        children: \"Reports\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                                value: \"warranties\",\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"search\",\n                                                        className: \"text-black\",\n                                                        children: \"Search\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                id: \"search\",\n                                                                placeholder: \"Search by BSL No, customer, or warranty code...\",\n                                                                value: searchTerm,\n                                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                                className: \"pl-10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"status\",\n                                                        className: \"text-black\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: statusFilter,\n                                                        onValueChange: setStatusFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                id: \"status\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                    placeholder: \"Select status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All Statuses\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 188,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"ACTIVE\",\n                                                                        children: \"Active\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 189,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"EXPIRED\",\n                                                                        children: \"Expired\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 190,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"PENDING\",\n                                                                        children: \"Pending\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"serviceCenter\",\n                                                        className: \"text-black\",\n                                                        children: \"Service Center\"\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                        value: serviceCenterFilter,\n                                                        onValueChange: setServiceCenterFilter,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                id: \"serviceCenter\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                    placeholder: \"Select service center\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                        value: \"all\",\n                                                                        children: \"All Service Centers\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 203,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    serviceCenters.map((center)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: center.name,\n                                                                            children: center.name\n                                                                        }, center.id, false, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                            lineNumber: 204,\n                                                                            columnNumber: 53\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.Alert, {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_10__.AlertDescription, {\n                                                className: \"text-black\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-md\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                className: \"text-black\",\n                                                                children: \"BSL No / Warranty Code\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 223,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                className: \"text-black\",\n                                                                children: \"Customer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                className: \"text-black\",\n                                                                children: \"Machine\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                className: \"text-black\",\n                                                                children: \"Service Center\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                className: \"text-black\",\n                                                                children: \"Warranty Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                className: \"text-black\",\n                                                                children: \"Amount\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                className: \"text-black\",\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                                className: \"text-right text-black\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                                    children: isLoading ? // Loading skeleton\n                                                    Array.from({\n                                                        length: 5\n                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                        className: \"h-6 w-32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 38\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                        className: \"h-6 w-32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 38\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                        className: \"h-6 w-32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 38\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                        className: \"h-6 w-32\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 38\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                        className: \"h-6 w-24\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 38\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                        className: \"h-6 w-20\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 38\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                        className: \"h-6 w-20\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 245,\n                                                                        columnNumber: 38\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    className: \"text-right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                                                                        className: \"h-6 w-16 ml-auto\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 61\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, \"skeleton-\".concat(index), true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 40\n                                                        }, this)) : filteredWarranties.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            colSpan: 8,\n                                                            className: \"text-center py-8\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col items-center space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-8 w-8 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: \"No BLUESTAR warranties found\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                                                            href: \"/warranties/new?vendor=bluestar\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-4 w-4 mr-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                    lineNumber: 254,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                \"Add First BLUESTAR Warranty\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                            lineNumber: 253,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 74\n                                                    }, this) : filteredWarranties.map((warranty)=>{\n                                                        var _warranty_vendorSpecific, _warranty_machines_, _warranty_machines_1, _warranty_machines_2, _warranty_vendorSpecific1, _warranty_vendorSpecific2;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    className: \"text-black\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: warranty.bslNo\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: ((_warranty_vendorSpecific = warranty.vendorSpecific) === null || _warranty_vendorSpecific === void 0 ? void 0 : _warranty_vendorSpecific.bluestarWarrantyCode) || 'N/A'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 264,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    className: \"text-black\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: warranty.customer.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 269,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: warranty.customer.city\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 270,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    className: \"text-black\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium\",\n                                                                                children: (_warranty_machines_ = warranty.machines[0]) === null || _warranty_machines_ === void 0 ? void 0 : _warranty_machines_.product.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 275,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Model: \",\n                                                                                    (_warranty_machines_1 = warranty.machines[0]) === null || _warranty_machines_1 === void 0 ? void 0 : _warranty_machines_1.model.name\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 276,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"SN: \",\n                                                                                    (_warranty_machines_2 = warranty.machines[0]) === null || _warranty_machines_2 === void 0 ? void 0 : _warranty_machines_2.serialNumber\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 277,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    className: \"text-black\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"h-3 w-3 text-gray-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                        lineNumber: 283,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm\",\n                                                                                        children: ((_warranty_vendorSpecific1 = warranty.vendorSpecific) === null || _warranty_vendorSpecific1 === void 0 ? void 0 : _warranty_vendorSpecific1.bluestarServiceCenter) || 'N/A'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                        lineNumber: 284,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 282,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-gray-500 flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"h-3 w-3 text-gray-400\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                        lineNumber: 287,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: ((_warranty_vendorSpecific2 = warranty.vendorSpecific) === null || _warranty_vendorSpecific2 === void 0 ? void 0 : _warranty_vendorSpecific2.bluestarContactPerson) || 'N/A'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                        lineNumber: 288,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 286,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    className: \"text-black\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"h-4 w-4 text-gray-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 294,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: formatDate(warranty.warrantyDate)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 295,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 292,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    className: \"text-black\",\n                                                                    children: formatCurrency(warranty.bslAmount)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 298,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    children: getStatusBadge(warranty.status, warranty.warrantyDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                                    className: \"text-right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-end space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                                                                    href: \"/warranties/bluestar/\".concat(warranty.id),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                        lineNumber: 304,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                    lineNumber: 303,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                                                                    href: \"/warranties/bluestar/\".concat(warranty.id, \"/edit\"),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                        lineNumber: 309,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                    lineNumber: 308,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 307,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                size: \"sm\",\n                                                                                className: \"text-destructive hover:text-destructive\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                    lineNumber: 313,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, warranty.id, true, {\n                                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 72\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isLoading && filteredWarranties.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"Showing \",\n                                                filteredWarranties.length,\n                                                \" of \",\n                                                bluestarWarranties.length,\n                                                \" BLUESTAR warranties\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 63\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                                value: \"service-centers\",\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-black mb-2\",\n                                            children: \"BLUESTAR Service Centers\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Manage BLUESTAR service center information and contact details.\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add Service Center\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_11__.TabsContent, {\n                                value: \"reports\",\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"h-12 w-12 mx-auto text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-black mb-2\",\n                                            children: \"BLUESTAR Reports\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Generate specialized reports for BLUESTAR warranty management.\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Eye_FileDown_FileText_MapPin_Phone_Plus_Search_Shield_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Generate Report\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\bluestar\\\\page.tsx\",\n        lineNumber: 137,\n        columnNumber: 10\n    }, this);\n}\n_s(BluestarWarrantyPage, \"6bzdvm/2uGo/Cpa5u114yKBgKMc=\");\n_c1 = BluestarWarrantyPage;\n_s1(BluestarWarrantyPage, \"6bzdvm/2uGo/Cpa5u114yKBgKMc=\");\n_c = BluestarWarrantyPage;\nvar _c;\n$RefreshReg$(_c, \"BluestarWarrantyPage\");\nvar _c1;\n$RefreshReg$(_c1, \"BluestarWarrantyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/bluestar/page.tsx\n"));

/***/ })

});