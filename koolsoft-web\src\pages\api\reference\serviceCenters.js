import { PrismaClient } from '@prisma/client';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';

const prisma = new PrismaClient();

/**
 * Service Centers Reference Data API Handler
 * 
 * GET /api/reference/serviceCenters - Get all service centers with pagination and filtering
 * POST /api/reference/serviceCenters - Create a new service center (Admin/Manager only)
 */
export default async function handler(req, res) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    switch (req.method) {
      case 'GET':
        return await handleGet(req, res);
      case 'POST':
        return await handlePost(req, res, session);
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('Service Centers Reference API Error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    });
  } finally {
    await prisma.$disconnect();
  }
}

async function handleGet(req, res) {
  try {
    const { 
      skip = '0', 
      take = '10', 
      search = '', 
      vendor = 'all', 
      city = 'all',
      active = 'true'
    } = req.query;

    const skipNum = parseInt(skip);
    const takeNum = parseInt(take);

    // Build where clause
    const where = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { vendor: { contains: search, mode: 'insensitive' } },
        { city: { contains: search, mode: 'insensitive' } },
        { contactPerson: { contains: search, mode: 'insensitive' } }
      ];
    }
    
    if (vendor && vendor !== 'all') {
      where.vendor = vendor;
    }
    
    if (city && city !== 'all') {
      where.city = city;
    }
    
    if (active !== 'all') {
      where.active = active === 'true';
    }

    // Fetch service centers with pagination
    const [serviceCenters, totalCount] = await Promise.all([
      prisma.serviceCenter.findMany({
        where,
        skip: skipNum,
        take: takeNum,
        orderBy: [
          { vendor: 'asc' },
          { city: 'asc' },
          { name: 'asc' }
        ]
      }),
      prisma.serviceCenter.count({ where })
    ]);

    // Get filter options
    const [vendors, cities] = await Promise.all([
      prisma.serviceCenter.findMany({
        select: { vendor: true },
        where: { vendor: { not: null } },
        distinct: ['vendor'],
        orderBy: { vendor: 'asc' }
      }),
      prisma.serviceCenter.findMany({
        select: { city: true },
        where: { city: { not: null } },
        distinct: ['city'],
        orderBy: { city: 'asc' }
      })
    ]);

    return res.status(200).json({
      data: serviceCenters,
      pagination: {
        total: totalCount,
        skip: skipNum,
        take: takeNum,
        totalPages: Math.ceil(totalCount / takeNum)
      },
      filters: {
        vendors: vendors.map(v => v.vendor).filter(Boolean),
        cities: cities.map(c => c.city).filter(Boolean)
      }
    });

  } catch (error) {
    console.error('Error fetching service centers:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch service centers',
      details: error.message 
    });
  }
}

async function handlePost(req, res, session) {
  try {
    // Check if user is admin or manager
    if (!['ADMIN', 'MANAGER'].includes(session.user.role)) {
      return res.status(403).json({ error: 'Admin or Manager access required' });
    }

    const {
      name,
      vendor,
      address,
      city,
      state,
      pincode,
      phone,
      email,
      contactPerson,
      active = true
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({ error: 'Service center name is required' });
    }

    // Check for duplicate name
    const existingServiceCenter = await prisma.serviceCenter.findFirst({
      where: {
        name: {
          equals: name,
          mode: 'insensitive'
        }
      }
    });

    if (existingServiceCenter) {
      return res.status(400).json({ 
        error: 'A service center with this name already exists' 
      });
    }

    // Create service center
    const serviceCenter = await prisma.serviceCenter.create({
      data: {
        name,
        vendor,
        address,
        city,
        state,
        pincode,
        phone,
        email,
        contactPerson,
        active
      }
    });

    return res.status(201).json({
      message: 'Service center created successfully',
      data: serviceCenter
    });

  } catch (error) {
    console.error('Error creating service center:', error);
    return res.status(500).json({ 
      error: 'Failed to create service center',
      details: error.message 
    });
  }
}
