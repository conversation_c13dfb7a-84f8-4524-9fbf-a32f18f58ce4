{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/reference/[type]/route": "app/api/reference/[type]/route.js", "/api/warranties/route": "app/api/warranties/route.js", "/page": "app/page.js", "/warranties/alerts/page": "app/warranties/alerts/page.js", "/customers/page": "app/customers/page.js", "/dashboard/page": "app/dashboard/page.js", "/reference-data/page": "app/reference-data/page.js", "/amc/page": "app/amc/page.js", "/warranties/bluestar/page": "app/warranties/bluestar/page.js"}