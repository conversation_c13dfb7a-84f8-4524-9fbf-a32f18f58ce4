'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  Search, 
  Filter, 
  FileDown, 
  Plus, 
  Eye, 
  Edit, 
  Trash2, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Calendar
} from 'lucide-react';
import Link from 'next/link';

/**
 * In-Warranty Management Page
 * 
 * This page displays and manages products currently under warranty coverage.
 * It includes filtering, searching, and CRUD operations for in-warranty items.
 */
export default function InWarrantyPage() {
  const [warranties, setWarranties] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [customerFilter, setCustomerFilter] = useState('all');
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Load warranties from API
  useEffect(() => {
    const loadWarranties = async () => {
      try {
        setIsLoading(true);

        const response = await fetch('/api/warranties?status=ACTIVE', {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch warranties');
        }

        const data = await response.json();
        setWarranties(data.warranties || []);
        setError(null);
      } catch (err) {
        console.error('Error loading warranties:', err);
        setError('Failed to load warranties');
        setWarranties([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadWarranties();
  }, []);

  const getStatusBadge = (status: string, warrantyDate: string) => {
    const today = new Date();
    const warranty = new Date(warrantyDate);
    const daysUntilExpiry = Math.ceil((warranty.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    if (status === 'EXPIRED') {
      return <Badge variant="destructive" className="flex items-center space-x-1">
        <AlertTriangle className="h-3 w-3" />
        <span>Expired</span>
      </Badge>;
    }

    if (daysUntilExpiry <= 30) {
      return <Badge variant="secondary" className="flex items-center space-x-1 bg-yellow-100 text-yellow-800">
        <Clock className="h-3 w-3" />
        <span>Expiring Soon</span>
      </Badge>;
    }

    return <Badge variant="secondary" className="flex items-center space-x-1 bg-green-100 text-green-800">
      <CheckCircle className="h-3 w-3" />
      <span>Active</span>
    </Badge>;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN');
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const filteredWarranties = warranties.filter(warranty => {
    const matchesSearch = searchTerm === '' || 
      warranty.bslNo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      warranty.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      warranty.machines.some((machine: any) => 
        machine.serialNumber.toLowerCase().includes(searchTerm.toLowerCase())
      );

    const matchesStatus = statusFilter === 'all' || warranty.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const handleDelete = async (warrantyId: string, bslNo: string) => {
    if (!confirm(`Are you sure you want to delete warranty ${bslNo}? This action cannot be undone.`)) {
      return;
    }

    try {
      setDeletingId(warrantyId);

      const response = await fetch(`/api/warranties/${warrantyId}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || 'Failed to delete warranty');
      }

      // Remove from local state
      setWarranties(prev => prev.filter(w => w.id !== warrantyId));
      setError(null);
    } catch (error: any) {
      console.error('Error deleting warranty:', error);
      setError(error.message || 'Failed to delete warranty');
    } finally {
      setDeletingId(null);
    }
  };

  const handleExport = async () => {
    try {
      const response = await fetch('/api/warranties/export?status=ACTIVE&format=CSV', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to export warranties');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `in-warranty-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting warranties:', error);
      setError('Failed to export warranties');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="pt-6">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="space-y-2">
              <Label htmlFor="search" className="text-black">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search by BSL No, customer, or serial number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status" className="text-black">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="EXPIRED">Expired</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="customer" className="text-black">Customer</Label>
              <Select value={customerFilter} onValueChange={setCustomerFilter}>
                <SelectTrigger id="customer">
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Customers</SelectItem>
                  {/* TODO: Populate with actual customers */}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Error State */}
          {error && (
            <Alert className="mb-6">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-black">{error}</AlertDescription>
            </Alert>
          )}

          {/* Warranties Table */}
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-black">BSL No</TableHead>
                  <TableHead className="text-black">Customer</TableHead>
                  <TableHead className="text-black">Install Date</TableHead>
                  <TableHead className="text-black">Warranty Date</TableHead>
                  <TableHead className="text-black">Machines</TableHead>
                  <TableHead className="text-black">Amount</TableHead>
                  <TableHead className="text-black">Status</TableHead>
                  <TableHead className="text-right text-black">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={`skeleton-${index}`}>
                      <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-16" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-6 w-16 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredWarranties.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex flex-col items-center space-y-2">
                        <Shield className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-500">No in-warranty products found</p>
                        <Button asChild>
                          <Link href="/warranties/new?type=in-warranty">
                            <Plus className="h-4 w-4 mr-2" />
                            Add First In-Warranty Product
                          </Link>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredWarranties.map((warranty) => (
                    <TableRow key={warranty.id}>
                      <TableCell className="font-medium text-black">{warranty.bslNo}</TableCell>
                      <TableCell className="text-black">
                        <div>
                          <div className="font-medium">{warranty.customer.name}</div>
                          <div className="text-sm text-gray-500">{warranty.customer.city}</div>
                        </div>
                      </TableCell>
                      <TableCell className="text-black">{formatDate(warranty.installDate)}</TableCell>
                      <TableCell className="text-black">{formatDate(warranty.warrantyDate)}</TableCell>
                      <TableCell className="text-black">{warranty.numberOfMachines}</TableCell>
                      <TableCell className="text-black">{formatCurrency(warranty.bslAmount)}</TableCell>
                      <TableCell>{getStatusBadge(warranty.status, warranty.warrantyDate)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end space-x-2">
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/warranties/${warranty.id}`}>
                              <Eye className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/warranties/${warranty.id}/edit`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            onClick={() => handleDelete(warranty.id, warranty.bslNo)}
                            disabled={deletingId === warranty.id}
                          >
                            {deletingId === warranty.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination would go here */}
          {!isLoading && filteredWarranties.length > 0 && (
            <div className="flex items-center justify-between mt-4">
              <p className="text-sm text-gray-600">
                Showing {filteredWarranties.length} of {warranties.length} warranties
              </p>
              {/* TODO: Add pagination controls */}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
