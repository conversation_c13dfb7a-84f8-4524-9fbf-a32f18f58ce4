"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/warranties/layout",{

/***/ "(app-pages-browser)/./src/app/warranties/layout.tsx":
/*!***************************************!*\
  !*** ./src/app/warranties/layout.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WarrantyLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout */ \"(app-pages-browser)/./src/components/layout/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\nvar _s1 = $RefreshSig$();\n\n\n\n\n/**\n * Warranty Layout Component\n *\n * This component provides a consistent layout for all warranty-related pages\n * using the standardized DashboardLayout component with collapsible sidebar.\n */ function WarrantyLayout(param) {\n    let { children } = param;\n    _s();\n    _s1();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Determine the current page title and breadcrumbs based on the pathname\n    let pageTitle = 'Warranty Management';\n    let breadcrumbs = [\n        {\n            label: 'Dashboard',\n            href: '/'\n        },\n        {\n            label: 'Warranty Management',\n            href: '/warranties',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\layout.tsx\",\n                lineNumber: 29,\n                columnNumber: 11\n            }, this)\n        }\n    ];\n    // Update breadcrumbs and title based on current path\n    if (pathname === '/warranties/in-warranty') {\n        pageTitle = 'In-Warranty Management';\n        breadcrumbs.push({\n            label: 'In-Warranty',\n            current: true\n        });\n    } else if (pathname === '/warranties/out-warranty') {\n        pageTitle = 'Out-of-Warranty Management';\n        breadcrumbs.push({\n            label: 'Out-of-Warranty',\n            current: true\n        });\n    } else if (pathname === '/warranties/components') {\n        pageTitle = 'Component Tracking';\n        breadcrumbs.push({\n            label: 'Components',\n            current: true\n        });\n    } else if (pathname === '/warranties/status') {\n        pageTitle = 'Warranty Status Dashboard';\n        breadcrumbs.push({\n            label: 'Status Dashboard',\n            current: true\n        });\n    } else if (pathname === '/warranties/alerts') {\n        pageTitle = 'Warranty Alerts';\n        breadcrumbs.push({\n            label: 'Alerts',\n            current: true\n        });\n    } else if (pathname === '/warranties/bluestar') {\n        pageTitle = 'BLUESTAR Warranties';\n        breadcrumbs.push({\n            label: 'BLUESTAR',\n            current: true\n        });\n    } else if (pathname.includes('/warranties/') && pathname.includes('/edit')) {\n        pageTitle = 'Edit Warranty';\n        breadcrumbs.push({\n            label: 'Edit',\n            current: true\n        });\n    } else if (pathname.includes('/warranties/') && !pathname.includes('/new')) {\n        pageTitle = 'Warranty Details';\n        breadcrumbs.push({\n            label: 'Details',\n            current: true\n        });\n    } else if (pathname === '/warranties/new') {\n        pageTitle = 'New Warranty';\n        breadcrumbs.push({\n            label: 'New Warranty',\n            current: true\n        });\n    } else if (pathname === '/warranties') {\n        pageTitle = 'Warranty Management';\n        breadcrumbs[breadcrumbs.length - 1].current = true;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout__WEBPACK_IMPORTED_MODULE_3__.DashboardLayout, {\n        title: pageTitle,\n        requireAuth: true,\n        allowedRoles: [\n            'ADMIN',\n            'MANAGER',\n            'EXECUTIVE',\n            'USER'\n        ],\n        breadcrumbs: breadcrumbs,\n        children: children\n    }, void 0, false, {\n        fileName: \"G:\\\\projects\\\\KoolSoft\\\\koolsoft-web\\\\src\\\\app\\\\warranties\\\\layout.tsx\",\n        lineNumber: 91,\n        columnNumber: 10\n    }, this);\n}\n_s(WarrantyLayout, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c1 = WarrantyLayout;\n_s1(WarrantyLayout, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = WarrantyLayout;\nvar _c;\n$RefreshReg$(_c, \"WarrantyLayout\");\nvar _c1;\n$RefreshReg$(_c1, \"WarrantyLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/warranties/layout.tsx\n"));

/***/ })

});